@Library('eks-shared-lib-jkt')_
pipeline {
    environment {
        RepoName = "${DeploymentName}-${environ}"
        environ = "development"
        DeploymentName = "pluang-spark-batch-processing-jobs"
        BucketName = "de-pluang-snapshot-resources-dev-jkt"
        GitUrl = "https://github.com/emasdigi/PluangSparkBatchProcessingJobs.git"
        // Test environment variables
        SPARK_LOCAL_IP = "127.0.0.1"
        PYTHONPATH = "${WORKSPACE}/src"
        ENABLE_JSON_LOGGING = "false"
        // Java environment for PySpark
        JAVA_HOME = "/usr/lib/jvm/java-17-openjdk"
        // PySpark serialization compatibility
        PYSPARK_PYTHON = "python3"
        PYSPARK_DRIVER_PYTHON = "python3"
        // Java 17 compatibility options for PySpark
        JAVA_TOOL_OPTIONS = "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
    }
    options {
        ansiColor('xterm')
        buildDiscarder logRotator(
            numToKeepStr: "5"
        )
        disableConcurrentBuilds abortPrevious: true
        preserveStashes()
        timestamps()
    }
    agent {
        kubernetes {
            defaultContainer 'jnlp'
            yamlFile 'pluang-spark-batch-processing-jobs/agentpod.yaml'
        }
    }
    parameters {
        gitParameter (
            name: 'BRANCH_TAG',
            type: 'PT_BRANCH_TAG',
            branchFilter: 'origin/(.*)',
            defaultValue: 'development',
            quickFilterEnabled: true,
            useRepository: 'https://github.com/emasdigi/PluangSparkBatchProcessingJobs.git'
        )
    }
    stages {
        stage('run-tests') {
            steps {
                script {
                    container('aws') {
                        // Checkout the code
                        git branch: "${params.BRANCH_TAG}",
                            credentialsId: 'GitCreds',
                            url: "${GitUrl}"

                        echo "🧪 Setting up test environment..."

                        sh '''
                        # Debug Java environment
                        echo "🔍 Java Environment Debug:"
                        echo "JAVA_HOME: $JAVA_HOME"
                        java -version
                        echo "Java executable location: $(which java)"
                        ls -la $JAVA_HOME/bin/java || echo "Java not found at expected location"

                        # Upgrade pip and install requirements
                        pip install --upgrade pip
                        pip install --prefer-binary --timeout=300 --no-cache-dir -r requirements-test.txt

                        # Debug Spark startup
                        export PYSPARK_SUBMIT_ARGS="--conf spark.ui.showConsoleProgress=true pyspark-shell"
                        export PYSPARK_VERBOSE=1

                        # Add pytest.ini to register markers if missing
                        if [ ! -f pytest.ini ]; then
                        echo "[pytest]" > pytest.ini
                        echo "markers =" >> pytest.ini
                        echo "    integration: mark test as integration test" >> pytest.ini
                        fi

                        echo "🚀 Running test suite..."
                        python -m pytest tests/ -v --tb=short --junit-xml=test-results.xml
                        '''

                        echo "✅ All tests passed successfully!"

                        // Archive test results
                        publishTestResults testResultsPattern: 'test-results.xml'

                        echo "🧹 Cleaned up test environment"
                    }
                }
            }
        }
        stage('build-and-upload') {
            steps {
                script {
                    container('aws') {
                        // Checkout the code
                        git branch: "${params.BRANCH_TAG}",
                            credentialsId: 'GitCreds',
                            url: "${GitUrl}"

                        // Upload main.py
                        sh "aws s3 cp main.py s3://${BucketName}/dev-pipeline/pluang_spark_batch_processing_jobs/main.py"

                        // Upload config folder recursively
                        sh "aws s3 cp config/ s3://${BucketName}/dev-pipeline/pluang_spark_batch_processing_jobs/config/ --recursive --exclude secret.json"

                        // Zip src folder
                        sh '''
                        find src -type f > file_list.txt
                        python3 -c "import os, zipfile; zip = zipfile.ZipFile('src.zip', 'w', zipfile.ZIP_DEFLATED); zip.write('src', 'src'); [zip.write(os.path.join(dp, f), os.path.join('src', os.path.relpath(os.path.join(dp, f), 'src')).replace(os.sep, '/')) for dp, dn, filenames in os.walk('src') for f in filenames if not f.startswith('._') and '.DS_Store' not in f]; zip.close()"
                        '''

                        // Upload zipped src
                        sh "aws s3 cp src.zip s3://${BucketName}/dev-pipeline/pluang_spark_batch_processing_jobs/src.zip"
                    }
                }
            }
        }
    }
    post {
        success {
            sendNotifications "SUCCESS", "${environ}", 'NO'
        }
        failure {
            sendNotifications "FAILED", "${environ}", 'NO'
        }
    }
}
