#!/bin/bash

# Script to install test dependencies with robust error handling
# Works for both local development and CI environments

set -e  # Exit on any error

echo "🔧 Installing test dependencies with binary-preferred policy..."

# Activate virtual environment
source .venv/bin/activate

# Only upgrade pip (NOT setuptools)
echo "📦 Upgrading pip..."
pip install --upgrade pip

# Install build dependencies first from requirements-test.txt
echo "🔧 Installing build dependencies..."
pip install --prefer-binary "setuptools<60.0" "wheel>=0.37.0" "Cython<3.0.0"

# Clear pip cache to avoid issues with cached metadata
echo "🧹 Clearing pip cache..."
pip cache purge || echo "Could not clear pip cache"

# Install all dependencies from requirements-test.txt with fallback strategies
echo "📦 Installing test dependencies from requirements-test.txt..."
if pip install --prefer-binary --timeout=300 --no-cache-dir -r requirements-test.txt; then
    echo "✅ All dependencies installed successfully with prefer-binary"
elif pip install --only-binary=:all: --timeout=300 --no-cache-dir -r requirements-test.txt; then
    echo "✅ All dependencies installed successfully with binary-only"
else
    echo "⚠️ Fallback: Installing with default strategy..."
    pip install --timeout=300 --no-cache-dir -r requirements-test.txt
fi

echo "✅ All test dependencies installed successfully!"

# Verify critical packages
echo "🔍 Verifying installations..."
python -c "import numpy; print(f'NumPy version: {numpy.__version__}')" || echo "❌ NumPy not available"
python -c "import pandas; print(f'Pandas version: {pandas.__version__}')" || echo "❌ Pandas not available"
python -c "import pytest; print(f'Pytest version: {pytest.__version__}')" || echo "❌ Pytest not available"
python -c "import pyspark; print(f'PySpark version: {pyspark.__version__}')" || echo "❌ PySpark not available"

echo "🎉 Test environment setup complete!"
