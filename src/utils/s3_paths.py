class S3Paths:
    # raw_data
    topups_raw_data = "raw_data/topups"
    cashouts_raw_data = "raw_data/cashouts"
    crypto_currency_transactions_raw_data = "raw_data/crypto_currency_transactions"
    crypto_currency_pocket_transactions_raw_data = "raw_data/crypto_currency_pocket_transactions"
    crypto_currency_wallet_transfers_raw_data = "raw_data/crypto_currency_wallet_transfers"
    crypto_future_transactions_raw_data = "raw_data/crypto_futures_transactions"
    global_stock_transactions_raw_data = "raw_data/global_stock_transactions"
    options_contract_transactions_raw_data = "raw_data/options_contract_transactions"
    gold_transactions_raw_data = "raw_data/gold_transactions"
    gold_gift_transactions_raw_data = "raw_data/gold_gift_transactions"
    gold_withdrawals_raw_data = "raw_data/gold_withdrawals"
    gold_loans_raw_data = "raw_data/gold_loans"
    installment_payment_raw_data = "raw_data/installment_payments"
    fund_transactions_raw_data = "raw_data/fund_transactions"
    forex_top_ups_raw_data = "raw_data/forex_top_ups"
    forex_cash_outs_raw_data = "raw_data/forex_cash_outs"
    forex_transactions_raw_data = "raw_data/forex_transactions"
    ph_global_stock_accounts_raw_data = "raw_data/ph_global_stock_accounts"
    ph_global_stock_returns_raw_data = "raw_data/ph_global_stock_returns"

    # snapshots
    gold_accounts = "gold_accounts/t_2_files"
    ph_global_stock_accounts = "ph_global_stock_accounts"
    ph_global_stock_returns = "ph_global_stock_returns"
    user_tag_mappings = "user_tag_mappings/t_2_files"
    accounts = "accounts/snapshots"

    topups = "snapshots/topups"
    cashouts = "snapshots/cashouts"
    crypto_currency_transactions = "snapshots/crypto_currency_transactions"
    crypto_currency_pocket_transactions = "snapshots/crypto_currency_pocket_transactions"
    crypto_currency_wallet_transfers = "snapshots/crypto_currency_wallet_transfers"
    crypto_future_transactions = "snapshots/crypto_future_transactions"
    global_stock_transactions = "snapshots/global_stock_transactions"
    options_contract_transactions = "snapshots/options_contract_transactions"
    gold_transactions = "snapshots/gold_transactions"
    gold_gift_transactions = "snapshots/gold_gift_transactions"
    gold_withdrawals = "snapshots/gold_withdrawals"
    gold_loans = "snapshots/gold_loans"
    installment_payment = "snapshots/installment_payments"
    fund_transactions = "snapshots/fund_transactions"
    forex_top_ups = "snapshots/forex_top_ups"
    forex_cash_outs = "snapshots/forex_cash_outs"
    forex_transactions = "snapshots/forex_transactions"

    indo_stock_transactions = "snapshots/indo_stock_transactions_v2"
    indo_stock_wallet_transactions = "snapshots/indo_stock_wallet_transactions_v2"
    indo_stock_price_v2 = "indo_stock_prices_v2/indo_stock_one_day_ohlc_price_stats"
    indo_stock_corporate_action_v2 = "indo_stock_prices_v2/corporate_action"
    indo_stock_returns_v2 = "indo_stock_returns_v2"
    indo_stock_returns_v2_t_2_files = "indo_stock_returns_v2/t_2_files"
    indo_stock_returns_v2_snapshots = "indo_stock_returns_v2/snapshots"
    indo_stock_price_v2_watchlist_52_week_low_price = "predefined_watchlist/indo_stock_price_v2_watchlist/52_week_low_price"
    indo_stock_price_v2_watchlist_52_week_high_price = "predefined_watchlist/indo_stock_price_v2_watchlist/52_week_high_price"
    indo_stock_price_v2_watchlist_daily_volume = "predefined_watchlist/indo_stock_price_v2_watchlist/volume"
    indo_stock_price_v2_watchlist_max_dividend = "predefined_watchlist/indo_stock_price_v2_watchlist/max_dividend"
    crypto_currency_snapshot = "crypto_currency_returns/snapshots"
    crypto_currency_pocket_snapshot = "crypto_currency_pocket_returns/snapshot"
    forex_snapshot = "forex_returns/snapshots"
    fund_snapshot = "fund_returns/snapshots"
    global_stock_snapshot = "global_stock_returns/snapshots"
    global_stock_pocket_snapshot = "global_stock_pocket_returns/snapshots"
    indo_stock_wallet_v2 = "indo_stock_wallet_v2/t_2_files"
    gold_snapshot = "gold_returns/snapshots"
    bappebti_wallet_snapshot = "bappebti_wallets/snapshots"
    gold_gift_and_withdrawal_snapshot = "gold_gift_and_withdrawal_snapshot"
    leverage_wallet_accounts_snapshot = "leverage_wallet_accounts/t_2_files"
    stock_index_snapshot = "stock_index_decommission/snapshots"
    options_snapshot = "global_stock_options_accounts/snapshots"
    crypto_futures_snapshot = "crypto_future_positions/snapshots"
    global_stock_intraday_snapshot = "global_stock_intraday_accounts/snapshots"


    # calculations
    gold_maintenance_fees_s3_folder = "gold_maintenance/gold_maintenance_fees"
    gold_maintenance_fees_intermediate_folder = "gold_maintenance/gold_maintenance_fees_intermediate"
    ph_global_stock_dividend_folder = "ph_data/global_stocks_dividend/snapshots"
    portfolio_snapshot_folder = "portfolio/snapshots"

