from src.utils.spark_utils import *
from src.utils.date_utils import *

class IndoStockPriceWatchlist:
    """
    Class to process Indo stock price data, detect 52-week highs/lows, and publish to Kafka & S3.
    """

    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.logger.info("Initializing IndoStockPriceWatchlist class")

        self.config = config
        self.cutoff_ts = config.get("cutoff_ts")
        self.bucket = config["bucket"]
        self.logger.debug(f"Cutoff: {self.cutoff_ts}, Bucket: {self.bucket}")

        self.spark_utils = SparkUtils("IndoStockPriceWatchlist")
        self.spark = self.spark_utils.create_spark_session()
        self.date_utils = DateUtils()
        self.io_utils = IOUtils(self.spark, self.config)
        self.ops = Operations(self.spark)
        self.user_props = UserProperties(self.spark, self.config)
        self.s3_path = S3Paths()

        self.job_config = config.get("pre_define_watch_list", {}).get("indo_stocks_v2", {})
        self.kafka_topic = self.config.get("kafka_topics", {}).get("pre_define_watch_list_topic")

        self.logger.info("IndoStockPriceWatchlist class initialized")

    def _read_ohlc_data(self, date) -> DataFrame:
        """Reads OHLC data for Indo stocks from S3 based on the configured date offset."""
        self.logger.info("Starting _read_ohlc_data")
        
        s3_path = f"s3a://{self.bucket}/{self.s3_path.indo_stock_price_v2}/dt={date}/"
        self.logger.info(f"Reading OHLC data from: {s3_path}")
        df = self.io_utils.read_csv_file(s3_path)
        self.logger.info(f"Read {df.count()} OHLC records from {s3_path}")
        self.logger.info("Completed _read_ohlc_data")
        return df

    def _filter_and_map_indo_stocks(self) -> DataFrame:
        """Fetches active Indo stock list from Kafka and returns a mapped DataFrame."""
        self.logger.info("Starting _filter_and_map_indo_stocks")
        try:
            topic = self.config["kafka_topics"]["indo_stocks_v2_topic"]
            servers = self.config["bootstrap_servers"]

            self.logger.info(f"Fetching Indo stock status from Kafka topic: {topic}")
            df = (
                self.io_utils.read_from_kafka_in_memory(bootstrap_servers=servers, topics=topic)
                .select(
                    col("value.id").alias("indo_stock_id"),
                    col("value.code").alias("indo_stock_code"),
                    col("value.status").alias("status"),
                    col("value.updated").alias("updated"),
                    col("value.stock_type").alias("stock_type")
                )
                .distinct()
            )

            self.logger.debug(f"Kafka Indo stock raw count: {df.count()}")
            df = self.ops.de_dupe_dataframe(df, ["indo_stock_id"], "updated")
            self.logger.debug(f"After de-dupe count: {df.count()}")
            df = df.filter(col("status").isin("ACTIVE", "active"))
            self.logger.debug(f"After filtering ACTIVE status count: {df.count()}")
            result = df.select("indo_stock_id", "indo_stock_code")
            self.logger.info("Completed _filter_and_map_indo_stocks")
            return result

        except Exception as e:
            self.logger.error("Error fetching Indo stock status from Kafka", exc_info=True)
            raise

    def _calculate_52_week_high_low(self, ohlc_df: DataFrame, stock_map_df: DataFrame) -> DataFrame:
        """ Calculates 52-week high and low prices for Indo stocks."""
        self.logger.info("Starting _calculate_52_week_high_low")
        ohlc_df = ohlc_df.withColumn("candle_start_time", col("candle_start_time").cast(TimestampType()))
        end_dt = self.cutoff_ts
        start_dt = end_dt - timedelta(weeks=52)

        start_ts_utc = start_dt.astimezone(pytz.UTC)
        end_ts_utc = end_dt.astimezone(pytz.UTC)

        self.logger.info(f"Calculating 52-week high/low from {start_dt.date()} to {end_dt.date()}")

        filtered_ohlc = ohlc_df.filter((col("candle_start_time") >= start_ts_utc) & (col("candle_start_time") <= end_ts_utc))
        self.logger.debug(f"Filtered OHLC count for 52-week window: {filtered_ohlc.count()}")

        joined = filtered_ohlc.join(stock_map_df, on=ohlc_df["stock_code"] == stock_map_df["indo_stock_code"], how="left")
        self.logger.debug(f"Joined OHLC and stock_map_df count: {joined.count()}")

        result = (
            joined
            .groupBy("indo_stock_id", "stock_code")
            .agg(
                F.max("high_price").alias("week_52_high_price"),
                F.min("low_price").alias("week_52_low_price")
            )
        )
        self.logger.info(f"Calculated 52-week high/low for {result.count()} records")
        self.logger.info("Completed _calculate_52_week_high_low")
        return result

    def _create_price_list(self, df: DataFrame, exec_date: str, price_type: str) -> DataFrame:
        """ Creates a structured DataFrame for 52-week high/low prices."""
        self.logger.info(f"Starting _create_price_list for {price_type}")
        if df.isEmpty():
            self.logger.warning(f"No data for {price_type}, returning empty DataFrame")
            return self.spark.createDataFrame([], df.schema)

        df = df.withColumn("indoStocks", F.struct(*df.columns))
        result = (
            df.withColumn("asset_type", lit("indoStocks"))
              .groupBy("asset_type")
              .agg(F.collect_list("indoStocks").alias("indoStocks"))
              .withColumn("content", F.struct("indoStocks"))
              .drop("asset_type", "indoStocks")
              .withColumn("category", lit(f"{price_type}_52_week"))
              .withColumn("execution_date", lit(exec_date))
        )
        self.logger.info(f"Created price list for {price_type} with {result.count()} records")
        self.logger.info(f"Completed _create_price_list for {price_type}")
        return result

    def _publish_to_kafka(self, df: DataFrame, topic: str):
        """ Publishes the DataFrame to a Kafka topic."""
        self.logger.info(f"Starting _publish_to_kafka for topic {topic}")
        try:
            self.logger.info(f"Publishing DataFrame to Kafka topic: {topic}")
            df = df.withColumn("x_request_id", F.expr("uuid()"))
            df_kafka = df.select(
                col("category").cast(StringType()).alias("key"),
                F.to_json(F.struct(*df.columns)).alias("value"),
                F.array(F.struct(lit("x-request-id").alias("key"),
                                 col("x_request_id").cast("binary").alias("value"))).alias("headers")
            )
            self.logger.debug(f"Kafka DataFrame count: {df_kafka.count()}")
            self.io_utils.write_data_in_kafka(df=df_kafka, topic=topic)
            self.logger.info(f"Published to Kafka topic: {topic}")
        except Exception as e:
            self.logger.error(f"Error publishing to Kafka topic: {topic}", exc_info=True)
            raise
        self.logger.info(f"Completed _publish_to_kafka for topic {topic}")

    def run(self):
        """ Main method to run the IndoStockPriceWatchlist job."""
        self.logger.info("Starting IndoStockPriceWatchlist job")
        exec_date = self.cutoff_ts.date()
        ohlc_df = self._read_ohlc_data(date=exec_date)
        self.logger.info(f"Read OHLC data for {ohlc_df.count()} records")

        stock_map_df = self._filter_and_map_indo_stocks()
        self.logger.info(f"Filtered indo stock map DataFrame with {stock_map_df.count()} records")
        
        result_df = self._calculate_52_week_high_low(ohlc_df, stock_map_df)
        self.logger.info(f"Calculated 52-week high/low for {result_df.count()} records")

        if result_df.isEmpty():
            self.logger.warning("No 52-week high/low data found, exiting job")
            self.spark_utils.stop_spark(self.spark)
            return
        
        if result_df.count() < 20:
            self.logger.warning(f"52-week high/low result count is less than 20: {result_df.count()}")

        df_low = result_df.select("indo_stock_id", "week_52_low_price")
        df_high = result_df.select("indo_stock_id", "week_52_high_price")

        df_low_final = self._create_price_list(df_low, exec_date, "low")
        self.logger.info(f"Created low price watchlist with {df_low_final.count()} records")
        df_high_final = self._create_price_list(df_high, exec_date, "high")
        self.logger.info(f"Created high price watchlist with {df_high_final.count()} records")

        s3_path_low = f"s3a://{self.bucket}/{S3Paths.indo_stock_price_v2_watchlist_52_week_low_price}/dt={exec_date}/"
        s3_path_high = f"s3a://{self.bucket}/{S3Paths.indo_stock_price_v2_watchlist_52_week_high_price}/dt={exec_date}/"

        self.logger.info(f"Writing low price watchlist to: {s3_path_low}")
        df_low_final.coalesce(1).write.mode("overwrite").json(s3_path_low)
        self.logger.info(f"Successfully wrote low price watchlist to: {s3_path_low}")

        self.logger.info(f"Writing high price watchlist to: {s3_path_high}")
        df_high_final.coalesce(1).write.mode("overwrite").json(s3_path_high)
        self.logger.info(f"Successfully wrote high price watchlist to: {s3_path_high}")

        self._publish_to_kafka(df_low_final, self.kafka_topic)
        self._publish_to_kafka(df_high_final, self.kafka_topic)
        self.logger.info("Completed IndoStockPriceWatchlist job")

        self.spark_utils.stop_spark(self.spark)
        self.logger.info("Spark session stopped")