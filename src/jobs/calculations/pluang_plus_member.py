from src.utils.spark_utils import *
from src.utils.date_utils import *

import time

class PluangPlusMember:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("pluang_plus_member_validation")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")
        self.pluang_plus_file_bucket = "s3a://{}".format(self.config["pluang_plus"]["file_bucket"])

        # Handling time based variables
        self.offset = self.config["offset"]
        self.execution_time = self.config["execution_time"]
        self.t_1 = self.config["t_1"]
        self.t_2 = self.config["t_2"]

        self.num_of_partition = 10
        self.sleep_time_mongo = 10

    def generate_plus_member_file(self, df_current_invested):
        def read_prev_plus_member_data():
            self.logger.info("reading old plus member file")
            df_prev_plus_member = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.bucket_path,
                                                                            self.config["pluang_plus_member_validity"][
                                                                                "plus_intermediate_file"],  self.t_2)).drop("is_whitelisted")
            return df_prev_plus_member

        def read_accounts_data():
            self.logger.info("reading accounts file to map user id")
            df_accounts = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.bucket_path,
                                                                    self.config["accounts_snapshot_folder"],  self.t_1)).filter(
                col("partner_id") == self.config["pluang_partner_id"]).select(
                col("id").alias("account_id"), col("user_id"))
            return df_accounts

        def read_whitelisted_plus_member_data():
            self.logger.info("reading whitelisted plus member file")
            df_whitelisted_plus_member = self.io_utils.read_csv_file("{}/{}/".format(self.bucket_path,
                                                                         self.config["pluang_plus_member_validity"][
                                                                             "whitelist_plus_member_file"])).distinct()
            return df_whitelisted_plus_member

        def read_user_tag_mappings_data():
            user_tag_mappings_path = "{}/{}/t_2_files/dt={}/".format(
                self.bucket_path,
                self.config["user_tag_mappings_snapshot_folder"],
                self.t_1
            )
            self.logger.info("reading user tag mapping from {}".format(user_tag_mappings_path))
            df_user_tag_mappings =  self.io_utils.read_csv_file(user_tag_mappings_path).select("user_id", "tag_id", "tag_pool_id", "tag_name").filter(
                col("tag_id") == self.config["plus_member_tag_id"])
            return df_user_tag_mappings

        def prepare_pluang_plus_member_data( df_current_invested, df_accounts, df_whitelisted_plus_member):
            df_current_invested = df_current_invested.select(col("account_id"),
                                                             col("invested_value").alias("current_invested_value"))

            self.logger.info("Joining current invested value with accounts to get user id")
            df_current_invested = df_current_invested.join(df_accounts, on=["account_id"], how="full").filter(
                col("user_id").isNotNull() & col("current_invested_value").isNotNull())

            self.logger.info("Joining current invested value with whitelisted users")
            df_current_invested = df_current_invested.join(df_whitelisted_plus_member, on=["account_id", "user_id"],
                                                           how="full").withColumn("is_whitelisted",
                                                                                  when(col("is_whitelisted") == True,
                                                                                       col("is_whitelisted").cast(
                                                                                           BooleanType())).otherwise(False))
            return df_current_invested

        def calculate_plus_member_status(df_prev_plus_member, df_current_invested, df_user_tag_mappings):
            current_date = DateUtils.get_jkt_date(self.offset)

            self.logger.info("joining  current invested value with prev plus member")
            df_plus_joined = df_prev_plus_member.join(df_current_invested, on=["user_id", "account_id"], how="full").filter(
                (col("plus_status").isNotNull()) | (
                            col("current_invested_value") >= self.config["pluang_plus_member_validity"][
                        "invested_value_threshold"]) | (col("is_whitelisted") == True))
            df_plus_joined = df_plus_joined.withColumn("invested_value", col("current_invested_value"))
            df_plus_joined = df_plus_joined.withColumn("grace_period", when(
                (col("invested_value") >= self.config["pluang_plus_member_validity"]["invested_value_threshold"]),
                0).otherwise(col("grace_period") + 1))
            df_plus_joined = df_plus_joined.withColumn("current_plus_status", when(((col("grace_period") <= self.config[
                "pluang_plus_member_validity"]["grace_period"]) | (col("is_whitelisted") == True)), True).otherwise(False))
            df_plus_joined = df_plus_joined.withColumn("created", when(col("plus_status").isNull(), current_date).otherwise(
                col("created")))
            df_plus_joined = df_plus_joined.withColumn("updated", when(
                ((col("plus_status").isNull()) | (col("current_plus_status") != col("plus_status"))),
                current_date).otherwise(col("updated")))
            df_plus_joined = df_plus_joined.withColumn("plus_status", col("current_plus_status")).drop(
                "current_plus_status").drop("current_invested_value")

            df_user_tag_mappings = df_user_tag_mappings.select("user_id", "tag_id", "tag_pool_id", "tag_name").filter(
                col("tag_id") == self.config["plus_member_tag_id"])
            df_plus_joined = df_plus_joined.join(df_user_tag_mappings, on=["user_id"], how="full")
            df_plus_joined = df_plus_joined.withColumn("processing_date", lit( self.t_1))
            df_plus_joined = df_plus_joined.withColumn("remove_today", when(
                ((col("plus_status") == False) | (col("plus_status").isNull())) & (col("tag_name").isNotNull()),
                True).otherwise(False))
            df_plus_joined = df_plus_joined.withColumn("add_today",
                                                       when((col("plus_status") == True) & (col("tag_name").isNull()),
                                                            True).otherwise(False))
            df_plus_joined = df_plus_joined.drop("tag_id", "tag_pool_id", "tag_name")
            return df_plus_joined

        def validate_user_account_mapping(df_plus_joined):
            count_account_id_validation = df_plus_joined.groupBy("user_id").agg(
                count("account_id").alias("count_account_id")).filter(col("count_account_id") > 1).count()
            if count_account_id_validation > 0:
                raise Exception("There are {} users which has multiple account ids".format(count_account_id_validation))

        self.logger.info("generating pluang plus member file")

        # Read all required data
        df_prev_plus_member = read_prev_plus_member_data()
        df_accounts = read_accounts_data()
        df_whitelisted_plus_member = read_whitelisted_plus_member_data()
        df_user_tag_mappings = read_user_tag_mappings_data()

        # Process invested value data
        df_current_invested = prepare_pluang_plus_member_data(df_current_invested, df_accounts, df_whitelisted_plus_member)
        # Join and calculate plus member status
        df_plus_joined = calculate_plus_member_status(df_prev_plus_member, df_current_invested, df_user_tag_mappings)
        # Validate data integrity
        validate_user_account_mapping(df_plus_joined)

        # Write results
        self.write_plus_member_results(df_plus_joined)
        self.logger.info("plus member calculation is successful")

    def write_plus_member_results(self, df_plus_joined):
        def write_plus_add_file(df_plus_joined, part_file_batch_size):
            df_today_plus_add = df_plus_joined.filter(col("add_today") == True)
            count_today_plus_add = df_today_plus_add.count()
            if count_today_plus_add > 0:
                self.logger.info("writing today's plus member add file with count {}".format(count_today_plus_add))
                num_add_partitions = max(1, count_today_plus_add // part_file_batch_size)
                self.io_utils.write_csv_file(df_today_plus_add.select(col("user_id").alias("userIds")),
                                             "{}/{}/dt={}/".format(
                                                 self.pluang_plus_file_bucket,
                                                 self.config["pluang_plus"]["add_plus_file"], self.t_1),
                                             num_add_partitions)
            else:
                self.logger.info("No User id to add in plus member")

        def write_plus_remove_file(df_plus_joined, part_file_batch_size):
            df_today_plus_remove = df_plus_joined.filter(col("remove_today") == True)
            count_today_plus_remove = df_today_plus_remove.count()
            if count_today_plus_remove > 0:
                self.logger.info(
                    "writing today's plus member remove file with count {}".format(count_today_plus_remove))
                num_remove_partitions = max(1, count_today_plus_remove // part_file_batch_size)
                self.io_utils.write_csv_file(df_today_plus_remove.select(col("user_id").alias("userIds")),
                                             "{}/{}/dt={}/".format(
                                                 self.pluang_plus_file_bucket,
                                                 self.config["pluang_plus"]["remove_plus_file"],
                                                 self.t_1), num_remove_partitions)
            else:
                self.logger.info("No User id to remove in plus member")
            self.logger.info("plus member calculation is successful")

        self.logger.info("writing new plus member file")
        self.io_utils.write_csv_file(df_plus_joined.filter(col("account_id").isNotNull()),
                                     "{}/{}/dt={}/".format(self.bucket_path,
                                                           self.config["pluang_plus_member_validity"][
                                                               "plus_intermediate_file"], self.t_1))
        part_file_batch_size = self.config["pluang_plus_member_validity"]["max_records_per_file"]
        # Write add file
        write_plus_add_file(df_plus_joined, part_file_batch_size)

        # Write remove file
        write_plus_remove_file(df_plus_joined, part_file_batch_size)

    def get_realised_gain(self):
        crypto_realised_gain = self.io_utils.read_csv_file(
            "{}/crypto_currency_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id",
                                                                                                     "realised_gain")

        fund_realised_gain = self.io_utils.read_csv_file( "{}/fund_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id", "realised_gain_idr").withColumnRenamed("realised_gain_idr", "realised_gain")
        forex_realised_gain = self.io_utils.read_csv_file( "{}/forex_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id", "realised_gain")

        stock_index_realised_gain = self.io_utils.read_csv_file( "{}/{}/".format(self.bucket_path, self.config["stock_index_folder"])).select( "account_id", "realised_gain_idr").withColumnRenamed("realised_gain_idr", "realised_gain")

        gold_realised_gain = self.io_utils.read_csv_file(
            "{}/gold_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id", "realised_gain")
        global_stock_realised_gain = self.io_utils.read_csv_file( "{}/global_stock_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id", "realised_gain_in_idr").withColumnRenamed("realised_gain_in_idr", "realised_gain")

        pocket_global_stock_realised_gain = self.io_utils.read_csv_file( "{}/global_stock_pocket_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id", "realised_gain_in_idr").withColumnRenamed("realised_gain_in_idr", "realised_gain")
        pocket_crypto_currency_realised_gain = self.io_utils.read_csv_file( "{}/crypto_currency_pocket_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)).select("account_id", "realised_gain")
        options_realised_gain = self.io_utils.read_csv_file("{}/{}/t_2_files/dt={}/".format(self.bucket_path,
                                                                             self.config["global_stock_options_accounts_folder"], self.t_1)).select("account_id", "realised_gain_in_idr").withColumnRenamed(
            "realised_gain_in_idr", "realised_gain")

        crypto_future_gain = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, self.config["crypto_future_snapshot_folder"], self.t_1)).withColumn("realised_gain",
                        col("realised_gain") * get_json_object(col("info"), "$.orderUpdateEvent.settleAssetMidPrice").cast("double")).select("account_id", "realised_gain")

        realised_gain = crypto_realised_gain.union(pocket_crypto_currency_realised_gain).union(
            fund_realised_gain).union(forex_realised_gain).union(stock_index_realised_gain).union(
            gold_realised_gain).union(global_stock_realised_gain).union(pocket_global_stock_realised_gain).union(
            options_realised_gain).union(crypto_future_gain)
        realised_gain = realised_gain.groupBy("account_id").agg(sum("realised_gain").alias("realised_gain_value"))
        return realised_gain

    def fetch_assets_data(self):

        def _read_cashin_data():
            cashin_snapshot_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["cashin_snapshot_folder"], self.t_1)
            self.logger.info("reading Cashin Data from path {}".format(cashin_snapshot_path))
            df = self.io_utils.read_csv_file(cashin_snapshot_path).filter(col("wallet_type") != "OJK")
            df = df.filter(col("status").isin("COMPLETED", "SUCCESS")).select("account_id", "nominal")
            df = df.withColumnRenamed("nominal", "cashin")
            df = df.select("account_id", "cashin")
            df = df.groupBy("account_id").agg(sum("cashin").alias("total_topup"))
            cashin = df.select("account_id","total_topup")
            return cashin

        def _read_cashouts_data():
            cashouts_snapshot_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["cashouts_snapshot_folder"], self.t_1)
            self.logger.info("reading Cashout Data from path {}".format(cashouts_snapshot_path))
            df = self.io_utils.read_csv_file(cashouts_snapshot_path).filter(col("wallet_type") != "OJK")
            df = df.filter(col("status").isin("COMPLETED", "SUCCESS")).select("account_id", "amount")
            df = df.withColumnRenamed("amount", "cashout")
            df = df.select("account_id", "cashout")
            df = df.groupBy("account_id").agg(sum("cashout").alias("total_cashout")).withColumn("dt", lit(self.t_1))
            cashouts = df.select("account_id","total_cashout")
            return cashouts

        def _read_funds_data():
            funds_data_path = "{}/fund_returns/t_2_files/dt={}/".format(self.bucket_path, self.t_1)
            self.logger.info("reading funds data from path {}".format(funds_data_path))
            funds_data = self.io_utils.read_csv_file(funds_data_path).select("account_id", "total_quantity", "weighted_cost_idr")
            funds_data = funds_data.withColumn("fund_invested_idr", col("total_quantity") * col("weighted_cost_idr"))
            funds_data = funds_data.groupBy("account_id").agg(sum("fund_invested_idr").alias("total_fund_invested_idr"))
            return funds_data

        def _read_gold_gift_data():
            # read gold gift
            gold_gift_snapshot_path = "{}/{}/t_2_files/dt={}/".format(self.bucket_path,
                                                                  self.config["gold_gift_snapshot"],  self.t_1)
            self.logger.info("reading Gold gift from path {}".format(gold_gift_snapshot_path))
            gold_gift = self.io_utils.read_csv_file(gold_gift_snapshot_path)
            gold_gift = gold_gift.filter(col("status").isin(["ACCEPTED"]))
            gold_gift = self.ops.de_dupe_dataframe(gold_gift, ["id", "account_id"], "updated")
            gold_gift_send = gold_gift.filter(col("transaction_type") == "SEND").withColumn("total_gold_send", (
                        col("unit_price") * col("quantity")).cast(LongType())).select("account_id",
                                                                                      "total_gold_send").groupBy(
                "account_id").agg(sum("total_gold_send").alias("total_gold_send"))
            gold_gift_receive = gold_gift.filter(col("transaction_type") == "RECEIVE").withColumn("total_gold_receive", (
                        col("unit_price") * col("quantity")).cast(LongType())).select("account_id",
                                                                                      "total_gold_receive").groupBy(
                "account_id").agg(sum("total_gold_receive").alias("total_gold_receive"))
            return gold_gift_send, gold_gift_receive

        def _read_gold_withdrawal_data():
            # read withdrawal
            gold_withdrawal_snapshot_path = "{}/{}/t_2_files/dt={}/".format(self.bucket_path,
                                                                        self.config["gold_withdrawal_snapshot"],  self.t_1)
            self.logger.info("reading Gold Withdrawal from path {}".format(gold_withdrawal_snapshot_path))
            gold_withdrawal = self.io_utils.read_csv_file(gold_withdrawal_snapshot_path)
            gold_withdrawal = gold_withdrawal.filter(col("status").isin(["DELIVERED"]))
            gold_withdrawal = gold_withdrawal.withColumn("price", F.when(F.col("sell_price").isNull(),
                                                                         F.col("unit_price")).otherwise(
                F.col("sell_price")))
            gold_withdrawal = self.ops.de_dupe_dataframe(gold_withdrawal, ["id", "account_id"], "updated").withColumn(
                "total_gold_withdrawal", (col("price") * col("net_amount")).cast(LongType())).select("account_id","total_gold_withdrawal").groupBy(
                "account_id").agg(sum("total_gold_withdrawal").alias("total_gold_withdrawal"))
            return gold_withdrawal

        def _read_crypto_transfer_data():
            # read crypto currency transfer
            crypto_currency_wallet_transfers_path = "{}/{}/dt={}/".format(self.bucket_path,
                                                                                self.config["crypto_currency_wallet_transfers_de_dupe"],
                                                                                 self.t_1)
            self.logger.info("reading crypto currency wallet transfer {}".format(crypto_currency_wallet_transfers_path))
            crypto_currency_transfer = self.io_utils.read_csv_file(crypto_currency_wallet_transfers_path)
            crypto_currency_transfer = crypto_currency_transfer.filter(
                (col("status").isin(["SUCCESS"])) & (col("network") != "MARGIN_INTERNAL"))
            crypto_currency_deposit = crypto_currency_transfer.filter(col("transaction_type") == "DEPOSIT").withColumn(
                "total_crypto_receive", (col("unit_price") * col("total_quantity")).cast(LongType())).select("account_id", "total_crypto_receive").groupBy(
                "account_id").agg(sum("total_crypto_receive").alias("total_crypto_receive"))
            crypto_currency_withdrawal = crypto_currency_transfer.filter(
                col("transaction_type") == "WITHDRAWAL").withColumn("total_crypto_send",
                                                                    (col("unit_price") * col("total_quantity")).cast(
                                                                        LongType())).select("account_id",
                                                                                            "total_crypto_send").groupBy(
                "account_id").agg(sum("total_crypto_send").alias("total_crypto_send"))
            return crypto_currency_deposit, crypto_currency_withdrawal

        def _read_forex_topups_data():
            # read forex toups
            forex_topups_path = "{}/{}/dt={}/".format( self.bucket_path, self.config["forex_top_ups_dedupe"], self.t_1 )
            self.logger.info("reading forex top ups from path {}".format(forex_topups_path))
            forex_topups = self.io_utils.read_csv_file(forex_topups_path)
            forex_topups = forex_topups.filter(col("status") == "COMPLETED").withColumn("final_amount_in_idr", (
                col("final_amount").cast(DoubleType())) * col("unit_price")).groupBy(["account_id"]).agg(
                sum("final_amount_in_idr").alias("total_forex_topups_idr"))
            return forex_topups

        def _read_forex_cashouts_data():
            # read forex cashouts
            forex_cashouts_path = "{}/{}/dt={}/".format( self.bucket_path, self.config["forex_cash_outs_dedupe"], self.t_1 )
            self.logger.info("reading forex cash outs from path {}".format(forex_cashouts_path))
            forex_cashouts = self.io_utils.read_csv_file(forex_cashouts_path)
            forex_cashouts = forex_cashouts.filter(col("status") == "COMPLETED").withColumn("withdrawal_amount_in_idr", (
                col("withdrawal_amount").cast(DoubleType())) * col("unit_price")).groupBy(["account_id"]).agg(
                sum("withdrawal_amount_in_idr").alias("total_forex_cashouts_idr"))
            return forex_cashouts

        cashin = _read_cashin_data()
        cashouts = _read_cashouts_data()
        funds_data = _read_funds_data()
        gold_gift_send, gold_gift_receive = _read_gold_gift_data()
        gold_withdrawal = _read_gold_withdrawal_data()
        crypto_currency_deposit, crypto_currency_withdrawal = _read_crypto_transfer_data()
        forex_topups = _read_forex_topups_data()
        forex_cashouts = _read_forex_cashouts_data()

        joinedDF = cashin.join(cashouts, on=["account_id"], how="full") \
            .join(funds_data, on=["account_id"], how="full") \
            .join(gold_gift_send, on=["account_id"], how="full") \
            .join(gold_gift_receive, on=["account_id"], how="full") \
            .join(gold_withdrawal, on=["account_id"], how="full") \
            .join(crypto_currency_deposit, on=["account_id"], how="full") \
            .join(crypto_currency_withdrawal, on=["account_id"], how="full") \
            .join(forex_topups, on=["account_id"], how="full") \
            .join(forex_cashouts, on=["account_id"], how="full")
        return joinedDF.select("account_id", "total_topup", "total_cashout", "total_gold_send",
                                    "total_gold_receive", "total_gold_withdrawal", "total_crypto_receive",
                                    "total_crypto_send", "total_forex_topups_idr", "total_forex_cashouts_idr", "total_fund_invested_idr").fillna(0)

    def calculate_invested_value(self, assets_data):
        df_current_invested = assets_data.groupBy("account_id") \
            .agg(sum("total_topup").alias("total_topup"),
                 sum("total_cashout").alias("total_cashout"),
                 sum("total_fund_invested_idr").alias("total_fund_invested_idr"),
                 sum("total_gold_send").alias("total_gold_send"),
                 sum("total_gold_receive").alias("total_gold_receive"),
                 sum("total_gold_withdrawal").alias("total_gold_withdrawal"),
                 sum("total_crypto_receive").alias("total_crypto_receive"),
                 sum("total_crypto_send").alias("total_crypto_send"),
                 sum("total_forex_topups_idr").alias("total_forex_topups_idr"),
                 sum("total_forex_cashouts_idr").alias("total_forex_cashouts_idr"))
        portfolio = self.get_realised_gain()
        portfolio = portfolio.withColumn("positive_realised_gain", F.when(F.col("realised_gain_value") > 0,
                                                                        F.col("realised_gain_value")).otherwise(0))
        df_current_invested = df_current_invested.join(portfolio, on=["account_id"], how="full").fillna(0)
        df_current_invested = df_current_invested.withColumn("invested_value",
                                                             col("total_topup") + col("total_fund_invested_idr") - col("total_cashout") - col(
                                                                 "total_gold_send") + col("total_gold_receive") - col(
                                                                 "total_gold_withdrawal") + col(
                                                                 "total_crypto_receive") - col(
                                                                 "total_crypto_send") + col(
                                                                 "total_forex_topups_idr") - col(
                                                                 "total_forex_cashouts_idr") + col(
                                                                 "positive_realised_gain"))
        created = datetime.strptime(str(self.t_1), '%Y-%m-%d').replace(hour=00, minute=00, second=00,
                                                                       microsecond=000000)
        df_current_invested = df_current_invested.withColumn("created", lit(created))
        df_current_invested = df_current_invested.withColumn("account_id", col("account_id").cast(LongType()))
        df_current_invested = df_current_invested.withColumn("invested_value", floor(col("invested_value")))
        df_current_invested = df_current_invested.drop("positive_realised_gain")
        return df_current_invested

    def write_invested_amount_to_mongo(self, df_current_invested_filtered):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config['collection'] = self.config["data_store"]["invested_amount"]["collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
        mongo_write_config = {
            "uri": mongo_uri,
            "collection": self.config["data_store"]["invested_amount"]["collection"],
            "batch_size": "500",
            "mode": "append"
        }
        for i in range(0, self.num_of_partition):
            self.logger.info("writing data in mongo for partition number: {}".format(i))
            df = df_current_invested_filtered.filter(col("account_id") % self.num_of_partition == i)
            self.io_utils.write_dataset_to_mongo(df, mongo_write_config,"Pluang Plus Member Validity",
                                                 "insert", "{'accountId':1}")
            time.sleep(self.sleep_time_mongo)


    def handle_current_day_processing(self, df_current_invested):
        self.logger.info("Total Count before filter {}".format(df_current_invested.count()))
        previous_day_niv = self.io_utils.read_csv_file("{}/{}/latest/".format(self.bucket_path,
                                                                      self.config["pluang_plus_member_validity_snapshot"]))
        previous_day_niv = previous_day_niv.select(col("account_id"),
                                                   col("invested_value").alias("prev_invested_value"))
        df_current_invested_filtered = df_current_invested.join(previous_day_niv, on=["account_id"],
                                                                how="left").fillna(0)
        df_current_invested_filtered = df_current_invested_filtered.filter(
            (col("invested_value") != 0) | (col("prev_invested_value") != 0)).drop("prev_invested_value")

        self.logger.info("Total Count after filter {}".format(df_current_invested_filtered.count()))


        self.write_invested_amount_to_mongo(df_current_invested_filtered)

        # create plus members
        self.logger.info("generating pluang plus member file")
        df_current_invested = df_current_invested.select("account_id", "total_topup", "total_fund_invested_idr",
                                    "total_cashout", "total_gold_send",
                                    "total_gold_receive", "total_gold_withdrawal", "total_crypto_receive",
                                    "total_crypto_send", "total_forex_topups_idr", "total_forex_cashouts_idr",
                                    "realised_gain_value", "invested_value", "created").fillna(0)
        self.generate_plus_member_file(df_current_invested)
        self.io_utils.write_csv_file(df_current_invested, "{}/{}/latest/".format(self.bucket_path, self.config["pluang_plus_member_validity_snapshot"]), 3)

    def start_processing(self):
        assets_data = self.fetch_assets_data()
        df_current_invested = self.calculate_invested_value(assets_data)
        # write data to s3
        self.io_utils.write_csv_file(df_current_invested, "{}/{}/dt={}/".format(self.bucket_path, self.config["pluang_plus_member_validity_snapshot"], self.t_1),3)

        if self.execution_time == "gss_post_market_close":
            self.handle_current_day_processing(df_current_invested)

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
