from src.utils.spark_utils import *
from src.schema.indo_stocks_v2_schema import schema_for_indo_stock_returns
from src.utils.s3_paths import S3Paths


class IndoStockReturns:
    """
    IndoStockReturns handles the ETL process for Indonesian stock returns.
    """

    asset_name = "indo_stock"
    primary_keys = ["account_id", "stock_id"]

    def __init__(self, config: dict, **kwargs):
        """Initializes required utilities and configuration."""
        self.logger = get_logger()
        self.logger.info("Initializing IndoStockReturns class")
        self.config = config
        self.bucket = config["bucket"]
        self.spark_utils = SparkUtils("indo_stock_returns")
        self.spark = self.spark_utils.create_spark_session()
        self.s3_paths = S3Paths()
        self.date_utils = DateUtils()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, config)
        self.user_props = UserProperties(self.spark, config)
        self.t_1_date = config["t_1"]
        self.t_2_date = config["t_2"]
        self.t_0_date = self.date_utils.get_jkt_date(config["offset"])
        self.raw_bucket_folder = f"s3a://{self.bucket}/{self.config['raw_data_folder']}"
        self.logger.info("IndoStockReturns initialized successfully")

    def get_current_prices(self) -> DataFrame:
        """Reads latest close prices for stocks from S3."""
        s3_path = f"s3a://{self.bucket}/{self.s3_paths.indo_stock_price_v2}/dt={self.t_1_date}"
        self.logger.info(f"Reading current prices from {s3_path}")

        data = self.io_utils.read_csv_file(path=s3_path)
        current_prices_df = data.select(
            col("stock_code"),
            col("close_price").alias("mid_price")
        )

        self.logger.info("Current prices successfully loaded")
        return current_prices_df

    def get_indo_stock_mapping(self) -> DataFrame:
        """Fetches stock ID to code mapping from Kafka topic."""
        self.logger.info("Loading stock mapping from Kafka")

        mapping_df = self.io_utils.get_asset_data("indo_stocks_v2_topic", "kafka").select(
            col("id").alias("stock_id"),
            col("code")
        )

        self.logger.info("Stock mapping loaded")
        return mapping_df

    def calculate_returns(self, t0_df: DataFrame, current_prices: DataFrame) -> DataFrame:
        """Calculates unrealized gains using T0 snapshot and current prices."""
        self.logger.info("Starting returns calculation")

        try:
            missing_ids = (
                t0_df.select("stock_id")
                .join(current_prices.select("stock_id"), on="stock_id", how="left_anti")
                .distinct()
                .collect()
            )
            if missing_ids:
                self.logger.warning(f"Missing stock IDs in current prices: {[row['stock_id'] for row in missing_ids]}")

            result_df = (
                t0_df.join(broadcast(current_prices), "stock_id")
                .withColumnRenamed("mid_price", "unit_price")
                .withColumn("total_quantity", round(col("total_quantity").cast(DoubleType()), 2))
                .withColumn("weighted_cost", col("weighted_cost").cast(DoubleType()))
                .withColumn("total_value", floor(col("total_quantity") * col("unit_price")))
                .withColumn("unrealised_gain", col("total_value") - ceil(col("total_quantity") * col("weighted_cost")))
            )

            self.logger.info("Returns calculated")
            return result_df

        except Exception as e:
            self.logger.error(f"Error calculating returns: {e}")
            raise

    def get_snapshot_data(self) -> DataFrame:
        """Fetches, merges and deduplicates T1 and T2 snapshot data."""
        t1_path = f"{self.raw_bucket_folder}/{self.s3_paths.indo_stock_returns_v2}/dt={self.t_1_date}/"
        t2_path = f"s3a://{self.bucket}/{self.s3_paths.indo_stock_returns_v2_t_2_files}/dt={self.t_2_date}/"

        self.logger.info(f"Reading T1 data from {t1_path}")
        t1_df = self.io_utils.read_json_data(
            t1_path, is_raw=True, schema=schema_for_indo_stock_returns, return_empty_if_file_not_present=True
        )

        delete_path = f"{self.raw_bucket_folder}/{self.s3_paths.indo_stock_returns_v2}/dt=1970-01-01/*"
        deleted_ids = set(self.io_utils.read_deleted_records(path=delete_path))
        self.logger.info(f"Deleted records count: {len(deleted_ids)}")

        if deleted_ids:
            t1_df = t1_df.filter(~col("id").isin(deleted_ids))

        self.logger.info(f"T1 snapshot after deletion filter: {t1_df.count()} rows")

        self.logger.info(f"Reading T2 data from {t2_path}")
        t2_df = self.io_utils.read_csv_file(
            t2_path, schema=schema_for_indo_stock_returns
        )

        if deleted_ids:
            t2_df = t2_df.filter(~col("id").isin(deleted_ids))

        self.logger.info(f"T2 snapshot after deletion filter: {t2_df.count()} rows")

        t0_df = self._generate_t0(t1_df, t2_df)
        self._save_t0(t0_df, date = self.t_0_date)

        return t0_df.drop("updated", "created")

    def _generate_t0(self, t1_df: DataFrame, t2_df: DataFrame) -> DataFrame:
        """Unions T1 and T2 data and performs deduplication on primary keys."""
        self.logger.info("Generating deduplicated T0 snapshot")
        final_df = t1_df.unionByName(t2_df.select(*t1_df.columns))
        self.logger.info("T0 snapshot generation complete")
        return final_df

    def _save_t0(self, t0_df: DataFrame, date: str):
        """Persists T0 DataFrame to storage."""
        output_path = f"s3a://{self.bucket}/{self.s3_paths.indo_stock_returns_v2_t_2_files}/dt={date}"
        self.logger.info(f"Saving T0 data to {output_path}")

        self.io_utils.write_csv_file(t0_df, output_path)
        self.logger.info("T0 data saved")

    def save_returns(self, returns_df: DataFrame, dt: str):
        """Saves the returns data to output S3 path."""
        current_timestamp = self.date_utils.get_jkt_timestamp(offset=0)
        returns_df = returns_df.withColumn("created", lit(current_timestamp))

        output_path = f"s3a://{self.bucket}/{self.s3_paths.indo_stock_returns_v2_snapshots}/dt={dt}"
        self.logger.info(f"Saving returns to {output_path}")

        self.io_utils.write_csv_file(returns_df, output_path)
        self.logger.info("Returns snapshot saved")

    def run(self):
        """Main orchestration method for Indo stock return snapshot job."""
        self.logger.info("ETL process started")
        try:
            t0_df = self.get_snapshot_data()
            current_prices = self.get_current_prices()
            mapping_df = self.get_indo_stock_mapping()

            current_prices = current_prices.join(
                broadcast(mapping_df), current_prices.stock_code == mapping_df.code, "inner"
            ).drop("code")

            returns_df = self.calculate_returns(t0_df, current_prices)
            self.save_returns(returns_df, self.t_1_date)

            self.logger.info("ETL process completed successfully")
        except Exception as e:
            self.logger.error(f"ETL process failed: {e}")
            raise
