from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.jobs.trading_competition.trading_competition_structs import schema_for_batch_flash_games, schema_for_all_transactions
import builtins
python_round = builtins.round

class FlashGamesPnL:
    def __init__(self, config: dict, **kwargs):
        self.config = config
        self.logger = get_logger()
        self.flash_games = config.get("flash_games", {})
        self.current_flash_game = None
        self.current_flash_game_id = None
        self.flash_game_end_ts = None
        self.flash_game_start_ts = None

        # get utility objects
        self.spark_utils = SparkUtils("Flash Games PnL")
        self.spark = self.spark_utils.create_spark_session()
        self.io_utils = IOUtils(self.spark, self.config)
        self.ops = Operations(self.spark)

        # Storage Paths
        self.bucket_path = self.config.get("bucket_path")
        self.transactions_snapshot_path = "{}/{}/".format(self.bucket_path, self.config['snapshot_path'])
        self.batch_path = "{}/{}".format(self.bucket_path, self.config["batches"]["batch_file_path"])
        self.flash_games_batch_path = "{}/{}".format(self.bucket_path,
                                                     self.config["batches"]["flash_games_batch_file_path"])
        self.transactions_overwrite_path = "{}/{}/".format(self.bucket_path,
                                                           self.config['batches']['all_transaction_file_path'])
        self.trading_competition_id = self.config["trading_competition"]["id"]
        self.tier_snapshot_path = "{}/{}".format(self.bucket_path,
                                                 self.config['aum_tier_upgrade']['tier_snapshot_path'])

        self.key_cols = ["account_id"]
        self.value_cols = ["asset_type", "user_id", "leverage", "asset_id", "asset_sub_type", "transaction_id",
                           "created", "updated", "fees",
                           "updated_executed_quantity", "updated_executed_unit_price",
                           "transaction_type", "currency_to_idr", "transaction_time",
                           "current_unit_price", "current_currency_to_idr", "remaining_quantity", "is_pnl_eligible",
                           "realized_pnl", "unrealized_pnl", "execution_time", "row_number",
                           "trading_competition_start_time", "trading_competition_id", "flash_game_id"]

        # Handling Dates & Timestamps
        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(
            self.config["trading_competition"]["start_time"])
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2))

    def get_current_flash_game(self):
        if not self.flash_games:
            return None
        for game_name, game_data in self.flash_games.items():
            start_dt = DateUtils.get_utc_timestamp_from_string(game_data["start_ts"])
            end_dt = DateUtils.get_utc_timestamp_from_string(game_data["end_ts"])
            schedule_end_dt = DateUtils.get_utc_timestamp_from_string(game_data["schedule_end_ts"])

            if start_dt < self.utc_cutoff_ts <= schedule_end_dt:
                self.flash_game_start_ts = start_dt
                self.flash_game_end_ts = end_dt
                return {
                    "flash_game_name": game_name,
                    **game_data
                }
        return None

    def get_live_options_contracts_data(self, global_stock_ids):
        options_contracts = self.io_utils.read_parquet_data(
            "{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "options_contracts", self.t_1, self.h_1),
            None, False)
        if global_stock_ids:
            filtered_contracts = options_contracts.filter((col("global_stock_id").isin(global_stock_ids)))
        else:
            filtered_contracts = options_contracts
        asset_ids = filtered_contracts.select("id").rdd.flatMap(lambda x: x).collect()
        self.logger.info("Successfully fetched Options contracts id")
        return {"global_stock_options": asset_ids}

    def get_global_stocks_leverage_data(self):
        df = self.io_utils.read_from_kafka_in_memory(self.config["bootstrap_servers"], self.config["kafka_topics"]["global_stock_topic"])
        stock_list = df.filter((col("value.status") == "ACTIVE") & (col("value.stock_type") == "CFD_LEVERAGE")).select("value.id").distinct()
        stock_list = stock_list.withColumn("stock_type", lit("CFD_LEVERAGE")).withColumnRenamed("id", "global_stock_id")
        asset_ids = stock_list.select("global_stock_id").rdd.flatMap(lambda x: x).collect()
        self.logger.info("Successfully fetched Leverage stock ids from kafka")
        return {"global_stocks": asset_ids}

    def get_current_flash_game_asset_id(self):
        asset_data = []
        assets = self.current_flash_game.get("assets", {})
        global_stock_options_enabled = False
        global_stock_with_leverage_enabled = False
        global_stock_ids = []
        for asset_type, asset_ids in assets.items():
            if asset_type == "global_stocks":
                global_stock_ids.extend(asset_ids if asset_ids else [])
            if asset_type == "global_stock_options":
                global_stock_options_enabled = True
            if asset_type == "global_stock_with_leverage":
                global_stock_with_leverage_enabled = True
            if not asset_ids and asset_type not in ["global_stock_options", "global_stock_with_leverage"]:
                self.logger.info(f"All asset_id enabled from asset_type {asset_type}")
                asset_data.append({"asset_type": asset_type, "asset_id": 0})
            else:
                for asset_id in asset_ids:
                    asset_data.append({"asset_type": asset_type, "asset_id": int(asset_id)})
        self.logger.info(f"global_stock_options_enabled is {global_stock_options_enabled}")

        if global_stock_options_enabled:
            options_data = self.get_live_options_contracts_data(global_stock_ids)
            for asset_id in options_data.get("global_stock_options", []):
                asset_data.append({"asset_type": "global_stock_options", "asset_id": int(asset_id)})

        if global_stock_with_leverage_enabled:
            leverage_data = self.get_global_stocks_leverage_data()
            self.logger.info(f"Global Stock Leverage Asset ids fetched are {leverage_data.get('global_stocks', [])}")
            for asset_id in leverage_data.get("global_stocks", []):
                asset_data.append({"asset_type": "global_stocks", "asset_id": int(asset_id)})

        schema = StructType([
            StructField("asset_type", StringType(), True),
            StructField("asset_id", LongType(), True)
        ])
        return self.spark.createDataFrame(asset_data, schema=schema)

    def get_all_eligible_transactions(self, game_df, all_transactions):
        game = game_df.alias("g").select("asset_type", "asset_id")
        txn = all_transactions.alias("txn")

        join_condition = F.expr("""
            (g.asset_id = 0 AND g.asset_type = txn.asset_type)
            OR 
            (g.asset_id != 0 AND g.asset_type = txn.asset_type AND g.asset_id = txn.asset_id)
        """)
        eligible_txn = txn.join(game, on=join_condition, how="inner")
        self.logger.info(f"eligible_txn df count is {eligible_txn.count()}")
        eligible_txn = eligible_txn.select(
            col("account_id"),
            col("user_id").cast("long"),
            col("txn.asset_id").cast("long"),
            col("leverage"),
            col("fees"),
            col("txn.asset_type"),
            col("transaction_id").cast("long"),
            col("created"),
            col("updated"),
            col("updated_executed_quantity").cast("double"),
            col("updated_executed_unit_price").cast("double"),
            col("transaction_type"),
            col("currency_to_idr").cast("double"),
            col("transaction_time"),
            col("asset_sub_type"),
            col("current_unit_price"),
            col("current_currency_to_idr")
        )
        eligible_txn = eligible_txn.withColumn("created", col("created").cast(TimestampType())) \
            .withColumn("updated", col("updated").cast(TimestampType())) \
            .withColumn("transaction_time", col("transaction_time").cast(TimestampType()))

        eligible_txn.select(
            F.max("transaction_time").alias("max_transaction_time"),
            F.min("transaction_time").alias("min_transaction_time")
        ).show()
        return eligible_txn

    def create_initial_position(self, all_transactions):
        all_transactions = all_transactions.filter(
                                (col("transaction_time") < self.flash_game_start_ts) &
                                (col("asset_sub_type") != "crypto_future_funding_transactions")
)
        agg_df = all_transactions.groupBy("account_id", "user_id", "asset_id", "leverage", "asset_type").agg(
            F.coalesce(sum(when(col("transaction_type").isin(self.config["buy_types"]), col("updated_executed_quantity"))), lit(0.0)).alias(
                "buy_qty"),
            F.coalesce(sum(when(col("transaction_type").isin(self.config["sell_types"]), col("updated_executed_quantity"))), lit(0.0)).alias(
                "sell_qty")
        )
        agg_df = agg_df.withColumn("buy_qty", F.round(col("buy_qty"), 12)) \
                .withColumn("sell_qty", F.round(col("sell_qty"), 12))
        initials_df = agg_df.withColumn(
            "updated_executed_quantity",
            (col("buy_qty") - col("sell_qty")).cast("double")
        )
        initials_df = initials_df.withColumn("transaction_id", lit(0)) \
            .withColumn("transaction_time", lit(self.flash_game_start_ts)) \
            .withColumn("created", lit(self.flash_game_start_ts)) \
            .withColumn("updated", lit(self.flash_game_start_ts)) \
            .withColumn("updated_executed_unit_price", lit(0.0)) \
            .withColumn("fees", lit(0)) \
            .withColumn("currency_to_idr", lit(0)) \
            .withColumn("current_unit_price", lit(0.0)) \
            .withColumn("current_currency_to_idr", lit(0)) \
            .withColumn("transaction_type", lit("INITIAL_ASSET_BALANCE")) \
            .withColumn("asset_sub_type", when(col("asset_type") == "gold", "gold_transactions")
                        .when(col("asset_type") == "crypto_currency", "crypto_currency_transactions")
                        .when(col("asset_type") == "global_stocks", "global_stock_transactions")
                        .when(col("asset_type") == "global_stock_options", "options_contract_transactions")
                        .when(col("asset_type") == "crypto_futures", "crypto_future_trades")
                        .otherwise(None)
        )
        initials_df = initials_df.select(
            col("account_id"),
            col("user_id"),
            col("asset_id"),
            col("leverage"),
            col("fees"),
            col("asset_type"),
            col("transaction_id").cast("long"),  # integer -> string
            col("created"),
            col("updated"),
            col("updated_executed_quantity"),  # double -> string
            col("updated_executed_unit_price"),  # double -> string
            col("transaction_type"),
            col("currency_to_idr"),  # integer -> string
            col("transaction_time"),
            col("asset_sub_type"),
            col("current_unit_price"),
            col("current_currency_to_idr")
        )
        initials_df = initials_df.filter(col("updated_executed_quantity") != 0)
        problematic_rows = initials_df.filter((col("asset_type") != "crypto_futures") & (col("updated_executed_quantity") < 0))
        if problematic_rows.count() > 0:
            asset_pairs = [(row.asset_type, row.asset_id) for row in
                           problematic_rows.select("asset_id", "asset_type").distinct().collect()]
            self.logger.error(f"Found rows with updated_executed_quantity < 0 for non-crypto_futures asset_type. Asset Type/ID pairs: {asset_pairs}")
        initials_df.select(
            F.max("transaction_time").alias("max_transaction_time"),
            F.min("transaction_time").alias("min_transaction_time")
        ).show()
        return initials_df

    @staticmethod
    def create_batches(all_txn, buy_types, sell_types):
        all_sorted_txn = sorted(all_txn, key=lambda txn: txn["row_number"])
        result = []
        for index, transaction in enumerate(all_sorted_txn):
            row = transaction.asDict()
            currency_to_idr = row["currency_to_idr"]
            executed_unit_price = row["updated_executed_unit_price"]
            executed_quantity = row["updated_executed_quantity"]
            transaction_time = row["transaction_time"]
            leverage = row["leverage"] if row["leverage"] > 0 else 1

            if row["asset_type"] == "crypto_futures":
                row["realized_pnl"] = int(-1 * row["fees"])
                row["remaining_quantity"] = executed_quantity
                running_quantity = 0.0

                if row["transaction_type"] == "INITIAL_ASSET_BALANCE":
                    row["is_pnl_eligible"] = False

                for result_row in result:
                    if (result_row["asset_id"] == row["asset_id"]) and (
                            result_row["asset_sub_type"] == row["asset_sub_type"]):
                        if result_row["transaction_type"] == "INITIAL_ASSET_BALANCE":
                            running_quantity = result_row["updated_executed_quantity"]
                        elif result_row["transaction_type"] in buy_types:
                            running_quantity = running_quantity + result_row["updated_executed_quantity"]
                        else:
                            running_quantity = running_quantity - result_row["updated_executed_quantity"]
                if row["transaction_type"] in buy_types:
                    new_running_quantity = running_quantity + row["updated_executed_quantity"]
                else:
                    new_running_quantity = running_quantity - row["updated_executed_quantity"]
                new_running_quantity = python_round(new_running_quantity, 4)
                running_quantity = python_round(running_quantity, 4)

                if row["transaction_type"] != "INITIAL_ASSET_BALANCE":
                    initial_balance_row = next((r for r in result if
                                                r["asset_id"] == row["asset_id"] and r["asset_sub_type"] == row[
                                                    "asset_sub_type"] and r[
                                                    "transaction_type"] == "INITIAL_ASSET_BALANCE"), None)
                    if initial_balance_row and initial_balance_row["remaining_quantity"] != 0:
                        initial_qty = initial_balance_row["remaining_quantity"]
                        is_opposite_direction = (initial_qty > 0 and row["transaction_type"] in sell_types) or (
                                initial_qty < 0 and row["transaction_type"] in buy_types)

                        if is_opposite_direction:
                            settlement_qty = min(abs(initial_qty), executed_quantity)
                            initial_balance_row[
                                "remaining_quantity"] += settlement_qty if initial_qty < 0 else -settlement_qty
                            row["remaining_quantity"] = executed_quantity - settlement_qty
                            effective_quantity = row["remaining_quantity"]
                            if row["transaction_type"] in buy_types:
                                running_quantity = running_quantity + settlement_qty
                            else:
                                running_quantity = running_quantity - settlement_qty

                            if row["transaction_type"] in buy_types:
                                new_running_quantity = running_quantity + row["remaining_quantity"]
                            else:
                                new_running_quantity = running_quantity - row["remaining_quantity"]
                            row["realized_pnl"] = 0

                    if (new_running_quantity >= 0 and running_quantity < 0) or (
                            new_running_quantity <= 0 and running_quantity > 0):
                        for result_row in result:
                            if (result_row["asset_id"] == row["asset_id"]) and (
                                    result_row["asset_sub_type"] == row["asset_sub_type"]) and (
                                    row["transaction_type"] != "INITIAL_ASSET_BALANCE"):
                                remaining_quantity = result_row["remaining_quantity"] if result_row[
                                                                                             "transaction_type"] in buy_types else -1 * \
                                                                                                                                   result_row[
                                                                                                                                       "remaining_quantity"]
                                result_row["realized_pnl"] += int(remaining_quantity * (
                                        executed_unit_price * currency_to_idr -
                                        result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                                ))
                                result_row["remaining_quantity"] = 0.0
                        row["remaining_quantity"] = abs(new_running_quantity)

            elif row["transaction_type"] == "INITIAL_ASSET_BALANCE":
                row["remaining_quantity"] = executed_quantity
                row["is_pnl_eligible"] = False
            elif row["transaction_type"] in buy_types:
                row["remaining_quantity"] = executed_quantity
                row["is_pnl_eligible"] = True
            elif row["transaction_type"] in sell_types:
                remaining_sell_quantity = executed_quantity

                for result_row in result:
                    if (result_row["transaction_type"] == "INITIAL_ASSET_BALANCE") and (
                            result_row["asset_type"] == row["asset_type"]) and (
                            result_row["leverage"] == row["leverage"]) and (
                            result_row["asset_id"] == row["asset_id"]) and (
                            result_row["remaining_quantity"] > 0) and (
                            remaining_sell_quantity > 0):
                        quantity_settled = min(result_row["remaining_quantity"], remaining_sell_quantity)
                        remaining_sell_quantity -= quantity_settled
                        result_row["remaining_quantity"] -= quantity_settled
                        result_row["realized_pnl"] += 0

                for result_row in result:
                    if (result_row["transaction_type"] in buy_types) and (
                            result_row["asset_type"] == row["asset_type"]) and (
                            result_row["leverage"] == row["leverage"]) and (
                            result_row["asset_id"] == row["asset_id"]) and (
                            result_row["remaining_quantity"] > 0) and (
                            remaining_sell_quantity > 0):
                        quantity_sold = min(result_row["remaining_quantity"], remaining_sell_quantity)
                        remaining_sell_quantity -= quantity_sold
                        result_row["remaining_quantity"] -= quantity_sold

                        if (row["asset_sub_type"] != "crypto_currency_wallet_transfers") and (
                                row["asset_sub_type"] != "gold_withdrawals") and (
                                row["transaction_type"] != "AIRDROP_SELL"):
                            result_row["realized_pnl"] += int(quantity_sold * (
                                    executed_unit_price * currency_to_idr -
                                    result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                            ))
                row["remaining_quantity"] = remaining_sell_quantity
                row["is_pnl_eligible"] = False

            result.append(row)
        for index, result_row in enumerate(result):
            remaining_quantity = result_row["remaining_quantity"]
            if result_row["asset_sub_type"] == "crypto_future_trades" and result_row.get("is_pnl_eligible", True):
                remaining_quantity = remaining_quantity if result_row[
                                                               "transaction_type"] in buy_types else -1 * remaining_quantity
                result_row["unrealized_pnl"] = int(remaining_quantity * (
                        result_row["current_unit_price"] * result_row["current_currency_to_idr"]
                        - result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                ))
            elif (result_row["transaction_type"] in buy_types) and (
                    result_row["remaining_quantity"] > 0) and result_row.get("is_pnl_eligible", True):
                result_row["unrealized_pnl"] = int(remaining_quantity * (
                        result_row["current_unit_price"] * result_row["current_currency_to_idr"]
                        - result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                ))

        return result

    def get_batches(self, transactions):
        transactions = transactions.withColumn("remaining_quantity", lit(0.0)) \
            .withColumn("realized_pnl", lit(0)) \
            .withColumn("unrealized_pnl", lit(0)) \
            .withColumn("is_pnl_eligible", lit(True))

        transactions = transactions.withColumn("execution_time", lit(self.utc_cutoff_ts)) \
                                    .withColumn("trading_competition_start_time", lit(self.trading_competition_start_time)) \
                                    .withColumn("trading_competition_id", lit(self.trading_competition_id)) \
                                    .withColumn("flash_game_id", lit(self.current_flash_game_id)) \
                                    .withColumn("priority", when(col("transaction_type") == "INITIAL_ASSET_BALANCE", 1).otherwise(2))
        window_spec = Window.partitionBy(self.key_cols).orderBy(col("transaction_time").asc(), col("priority").asc())
        df_with_row_number = transactions.withColumn("row_number", row_number().over(window_spec)).drop("priority")

        grouped_df = (
            df_with_row_number
            .groupBy(self.key_cols)
            .agg(F.collect_list(F.struct(*[col(c) for c in self.value_cols])).alias("transactions"))
        )
        buy_types_list = self.config["buy_types"]
        sell_types_list = self.config["sell_types"]
        batch_udf = F.udf(lambda txns: FlashGamesPnL.create_batches(txns, buy_types_list, sell_types_list),
                          schema_for_batch_flash_games)

        batches = (
            grouped_df
            .withColumn("batch_result", batch_udf(col("transactions")))
            .select("account_id", "batch_result")
        )

        exploded_batch = batches.withColumn("batch_data", F.explode(col("batch_result"))).select(
            "account_id", "batch_data.*"
        )
        exploded_batch = exploded_batch.withColumn("remaining_quantity", F.round(col("remaining_quantity"), 12))
        return exploded_batch

    def cast_fields(self, all_txn):
        all_txn = all_txn.withColumn("current_unit_price", round(col("current_unit_price"), 12)) \
            .withColumn("remaining_quantity", round(col("remaining_quantity"), 12)) \
            .withColumn("updated_executed_quantity", round(col("updated_executed_quantity"), 12)) \
            .withColumn("updated_executed_unit_price", round(col("updated_executed_unit_price"), 12)) \
            .withColumn("current_currency_to_idr", col("current_currency_to_idr").cast(LongType())) \
            .withColumn("currency_to_idr", col("currency_to_idr").cast(LongType())) \
            .withColumn("realized_pnl", col("realized_pnl").cast(LongType())) \
            .withColumn("unrealized_pnl", col("unrealized_pnl").cast(LongType()))
        return all_txn

    def process_flash_game_pnl(self, batches):
        def get_user_details():
            user_details = self.io_utils.read_csv_file(
                "{}/dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1, self.h_1))
            user_details = user_details.select("account_id", "user_id", "name", "email", "trading_competition_id")
            return user_details

        def get_user_niv():
            niv_date = self.t_1 - timedelta(days=1)
            niv = self.io_utils.read_csv_file(
                "{}/{}/dt={}/".format(self.bucket_path, self.config['niv_path'], niv_date))
            niv = niv.select("account_id", "invested_value")
            return niv

        def get_user_gtv():
            gtv = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['gtv_path'], self.t_1, self.h_1))
            return gtv.select("account_id", "total_gtv")

        pnl = batches.groupby(["account_id"]).agg(sum("realized_pnl").alias("realized_pnl"),
                                                              sum("unrealized_pnl").alias("unrealized_pnl"))
        user_details = get_user_details()
        pnl = pnl.join(user_details, on=["account_id"], how="left").fillna({'realized_pnl': 0, 'unrealized_pnl': 0})
        pnl = pnl.withColumn("pnl", (col("realized_pnl") + col("unrealized_pnl")).cast("long"))
        pnl = pnl.withColumn("execution_time", lit(self.utc_cutoff_ts))

        niv = get_user_niv()
        pnl = pnl.join(niv, on=["account_id"], how="left").fillna({'invested_value': 0})

        gtv = get_user_gtv()
        pnl_gtv = pnl.join(gtv, on=["account_id"], how="left").fillna({'total_gtv': 0})

        pnl_window = Window.orderBy(col("pnl").desc(),
                                    col("total_gtv").desc(),
                                    col("invested_value").desc())
        pnl_df = pnl_gtv.withColumn("pnl_rank", F.rank().over(pnl_window)) \
                        .withColumn("trading_competition_id", lit(self.trading_competition_id)) \
                        .withColumn("flash_game_id", lit(self.current_flash_game_id))

        return pnl_df

    def write_flash_game_pnl_to_mongo(self, pnl_df):
        def build_user_properties(pnl_df):
            user_details = pnl_df.select("account_id", "user_id", "name","email", "trading_competition_id", "pnl", "total_gtv", "pnl_rank", "flash_game_id")
            current_timestamp = DateUtils.get_utc_timestamp()
            user_details = user_details.withColumn("createdAt", lit(self.flash_game_start_ts)) \
                .withColumn("updatedAt", lit(current_timestamp)) \
                .withColumn("last_calculated_at", lit(self.utc_cutoff_ts))
            user_details = user_details.select("flash_game_id", "user_id", "pnl", col("pnl_rank").alias("rank"),
                                               "account_id", "trading_competition_id", "createdAt", "updatedAt",
                                               "name", "email", col("total_gtv").alias("gtv"), "last_calculated_at")
            return user_details

        user_details = build_user_properties(pnl_df)
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config['collection'] = self.config["data_store"]["flash_game"]["collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
        mongo_write_config = {
            "uri": mongo_uri,
            "collection": self.config["data_store"]["flash_game"]["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        self.io_utils.write_dataset_to_mongo(user_details, mongo_write_config,
                                             "flash_game_mongo_write", "update",
                                             "{'userId':1,'tradingCompetitionId':1,'flashGameId':1}", add_created_at=False)

    def execute(self):
        # Set Current Flash Game Config
        self.current_flash_game = self.get_current_flash_game()
        if self.current_flash_game is None:
            self.logger.info("No active flash game found for the current UTC timestamp: {}. Exiting gracefully.".format(self.utc_cutoff_ts))
            return
        self.current_flash_game_id = self.current_flash_game['flash_game_name']
        current_flash_game_assets_df = self.get_current_flash_game_asset_id()
        self.logger.info(f"Current Flash Game: {self.current_flash_game_id}")
        self.io_utils.write_csv_file(current_flash_game_assets_df, "{}/{}/{}".format(self.bucket_path, self.config[
            'flash_games_assets_path'], self.current_flash_game_id))

        # Batch Creation for Flash Games
        all_transactions = self.io_utils.read_parquet_data(self.transactions_overwrite_path, schema_for_all_transactions, False)
        all_transactions = all_transactions.select("account_id", "user_id", "asset_id", "leverage", "fees", "asset_type", "transaction_id","created", "updated", "updated_executed_quantity", "updated_executed_unit_price","transaction_type", "currency_to_idr", "transaction_time", "asset_sub_type","current_unit_price", "current_currency_to_idr")

        eligible_txn = self.get_all_eligible_transactions(current_flash_game_assets_df, all_transactions)
        flash_game_txn = eligible_txn.filter(
            (col("transaction_time") >= self.flash_game_start_ts) & (col("transaction_time") <= self.flash_game_end_ts)
        )
        flash_game_users = flash_game_txn.select("account_id").distinct()
        self.logger.info("Flash game users count: {}".format(flash_game_users.count()))
        eligible_txn_filtered = eligible_txn.join(flash_game_users, on="account_id", how="inner")
        initial_asset_balance = self.create_initial_position(eligible_txn_filtered)
        transformed_flash_game_txn = flash_game_txn.union(initial_asset_balance).join(flash_game_users, on="account_id", how="inner")
        batches = self.get_batches(transformed_flash_game_txn)
        batches = self.cast_fields(batches)
        batches = batches.select("asset_id", "asset_type", "account_id", "user_id", "transaction_id",
                                 "asset_sub_type", "created", "updated", "transaction_time", "transaction_type",
                                 "leverage", "fees",
                                 "currency_to_idr", "current_unit_price", "current_currency_to_idr",
                                 "remaining_quantity", "realized_pnl", "unrealized_pnl", "execution_time",
                                 "trading_competition_start_time", "trading_competition_id", "flash_game_id",
                                 "row_number", "updated_executed_quantity",
                                 "updated_executed_unit_price")
        self.io_utils.write_parquet_file(batches,
                                         "{}/dt={}/hour={}/".format(self.flash_games_batch_path, self.t_1, self.h_1), 5)
        self.io_utils.write_csv_file(batches, "{}/current/".format(self.flash_games_batch_path), 5)

        # Process PnL for current game
        flash_game_pnl = self.process_flash_game_pnl(batches)
        self.io_utils.write_csv_file(flash_game_pnl, "{}/{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config[
            'flash_games_pnl_path'], self.current_flash_game_id, self.t_1, self.h_1), 1)
        self.io_utils.write_csv_file(flash_game_pnl, "{}/{}/current/".format(self.bucket_path, self.config[
            'flash_games_pnl_path']))

        # Write PnL to Mongo
        self.write_flash_game_pnl_to_mongo(flash_game_pnl)

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)